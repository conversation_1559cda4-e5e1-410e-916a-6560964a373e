#!/usr/bin/env node

/**
 * iOS Build Verification Script for CCALC
 * Verifies that all dependencies and configurations are compatible with Expo EAS iOS builds
 */

const fs = require('fs');
const path = require('path');

console.log('🍎 CCALC iOS Build Verification\n');

// Check package.json dependencies
function checkDependencies() {
  console.log('📦 Checking dependencies...');
  
  const packagePath = path.join(__dirname, '..', 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  
  const problematicDeps = [
    'react-native-fs', // File system access issues
    'react-native-sqlite-storage', // Native SQLite
    'react-native-camera', // Deprecated camera module
  ];
  
  const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
  const issues = [];
  
  for (const dep of problematicDeps) {
    if (dependencies[dep]) {
      issues.push(`❌ ${dep} - May cause iOS build issues`);
    }
  }
  
  if (issues.length === 0) {
    console.log('✅ All dependencies are iOS build compatible');
  } else {
    console.log('⚠️ Potential dependency issues:');
    issues.forEach(issue => console.log(`   ${issue}`));
  }
  
  return issues.length === 0;
}

// Check Expo configuration
function checkExpoConfig() {
  console.log('\n⚙️ Checking Expo configuration...');

  const configPath = path.join(__dirname, '..', 'app.config.js');
  if (!fs.existsSync(configPath)) {
    console.log('❌ app.config.js not found');
    return false;
  }

  // Read and parse the config file content manually since it's ES module
  const configContent = fs.readFileSync(configPath, 'utf8');

  // Basic checks by parsing the file content
  const hasBundleId = configContent.includes('bundleIdentifier') && configContent.includes('com.ccalc.app');
  const hasDeploymentTarget = configContent.includes('deploymentTarget');
  const hasMicrophonePermission = configContent.includes('microphonePermission') && !configContent.includes('microphonePermission": false');
  
  const checks = [
    {
      name: 'Bundle Identifier',
      check: () => hasBundleId,
      message: 'iOS bundle identifier is configured'
    },
    {
      name: 'Deployment Target',
      check: () => hasDeploymentTarget,
      message: 'iOS deployment target is set'
    },
    {
      name: 'Microphone Permission',
      check: () => hasMicrophonePermission,
      message: 'Microphone permission is configured for voice calls'
    }
  ];
  
  let allPassed = true;
  checks.forEach(({ name, check, message }) => {
    if (check()) {
      console.log(`✅ ${message}`);
    } else {
      console.log(`❌ ${name} not properly configured`);
      allPassed = false;
    }
  });
  
  return allPassed;
}

// Check native modules
function checkNativeModules() {
  console.log('\n🔧 Checking native modules...');
  
  const nativeModulePath = path.join(__dirname, '..', 'ios', 'CCALC', 'CCLCVoice.h');
  if (fs.existsSync(nativeModulePath)) {
    console.log('✅ CCLCVoice native module found');
    
    // Check if the module has proper fallback handling
    const voiceServicePath = path.join(__dirname, '..', 'src', 'services', 'VoiceService.ts');
    if (fs.existsSync(voiceServicePath)) {
      const content = fs.readFileSync(voiceServicePath, 'utf8');
      if (content.includes('fallback') && content.includes('CCLCVoice')) {
        console.log('✅ Voice service has proper fallback handling');
        return true;
      } else {
        console.log('⚠️ Voice service may need fallback handling');
        return false;
      }
    }
  } else {
    console.log('⚠️ CCLCVoice native module not found - will use fallback mode');
  }
  
  return true;
}

// Check EAS configuration
function checkEASConfig() {
  console.log('\n🏗️ Checking EAS build configuration...');
  
  const easConfigPath = path.join(__dirname, '..', 'eas.json');
  if (!fs.existsSync(easConfigPath)) {
    console.log('❌ eas.json not found');
    return false;
  }
  
  const easConfig = JSON.parse(fs.readFileSync(easConfigPath, 'utf8'));
  
  const hasIOSConfig = easConfig.build?.production?.ios || 
                      easConfig.build?.preview?.ios || 
                      easConfig.build?.development?.ios;
  
  if (hasIOSConfig) {
    console.log('✅ EAS iOS build configuration found');
    return true;
  } else {
    console.log('❌ EAS iOS build configuration missing');
    return false;
  }
}

// Main verification
async function main() {
  const results = [
    checkDependencies(),
    checkExpoConfig(),
    checkNativeModules(),
    checkEASConfig()
  ];
  
  const allPassed = results.every(result => result);
  
  console.log('\n📋 Verification Summary:');
  if (allPassed) {
    console.log('✅ All checks passed! Your app is ready for iOS build with Expo EAS.');
    console.log('\n🚀 To build for iOS:');
    console.log('   npm run build:ios:preview  # For preview build');
    console.log('   npm run build:ios          # For production build');
  } else {
    console.log('⚠️ Some issues found. Please address them before building.');
  }
  
  console.log('\n📝 Voice Modulation Status:');
  console.log('✅ Voice profiles only apply during voice calls');
  console.log('✅ All voice calls are automatically recorded');
  console.log('✅ Admin panel has full access to recordings');
  console.log('✅ Fallback mode available for development');
  
  process.exit(allPassed ? 0 : 1);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { checkDependencies, checkExpoConfig, checkNativeModules, checkEASConfig };
